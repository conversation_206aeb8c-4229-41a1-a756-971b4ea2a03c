<template>
  <div class="camera-management-page">
    <!-- 页面主体内容 -->
    <div class="page-content">
      <!-- 左侧：设备选择面板 -->
      <div class="left-panel">
        <!-- 摄像头统计概览 -->
        <div class="camera-stats-dashboard">
          <div class="stats-header">
            <h3><i class="fas fa-video"></i> 监控系统概览</h3>
            <div class="refresh-btn" @click="fetchDeviceData">
              <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
            </div>
          </div>
          <div class="stats-grid">
            <div class="stat-card total">
              <div class="stat-icon">
                <i class="fas fa-video"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ cameraStats.total }}</div>
                <div class="stat-label">摄像头总数</div>
                <div class="stat-trend">
                  <div class="trend-indicator positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>实时监控</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="stat-card online">
              <div class="stat-icon">
                <i class="fas fa-play-circle"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ cameraStats.online }}</div>
                <div class="stat-label">在线监控</div>
                <div class="stat-progress">
                  <div class="progress-bar" :style="{ width: onlinePercentage + '%' }"></div>
                </div>
              </div>
            </div>
            <div class="stat-card offline">
              <div class="stat-icon">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ cameraStats.offline }}</div>
                <div class="stat-label">离线设备</div>
                <div class="stat-alert" v-if="cameraStats.offline > 0">
                  <i class="fas fa-bell"></i>
                  <span>需要关注</span>
                </div>
              </div>
            </div>
            <div class="stat-card devices">
              <div class="stat-icon">
                <i class="fas fa-server"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ cameraStats.devices }}</div>
                <div class="stat-label">监控设备</div>
                <div class="stat-detail">
                  <span>活跃: {{ activeDevices }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备选择列表 -->
        <div class="device-selection-section">
          <div class="section-header">
            <h3><i class="fas fa-server"></i> 设备选择</h3>
            <div class="header-info">
              <span class="device-count">{{ deviceList.length }} 台设备</span>
            </div>
          </div>

          <div v-if="loading" class="loading-container">
            <div class="loading-spinner">
              <i class="fas fa-spinner fa-spin"></i>
            </div>
            <span>加载设备数据中...</span>
          </div>

          <div v-else-if="deviceList.length === 0" class="no-data-container">
            <i class="fas fa-server"></i>
            <h3>暂无监控设备</h3>
            <p>项目中暂无配置监控设备</p>
          </div>

          <div v-else class="device-selection-list">
            <!-- 全部设备选项 -->
            <div
              class="device-selection-item all-devices"
              :class="{ 'selected': selectedDevice === null }"
              @click="selectAllDevices"
            >
              <div class="device-icon">
                <i class="fas fa-th-large"></i>
              </div>
              <div class="device-info">
                <div class="device-name">全部摄像头</div>
                <div class="device-meta">显示所有设备的摄像头</div>
              </div>
              <div class="device-stats">
                <div class="camera-count">{{ cameraStats.total }}</div>
                <div class="count-label">摄像头</div>
              </div>
            </div>

            <!-- 具体设备列表 -->
            <div
              v-for="device in deviceList"
              :key="device.id"
              class="device-selection-item"
              :class="{ 'selected': selectedDevice && selectedDevice.id === device.id }"
              @click="selectDevice(device)"
            >
              <div class="device-icon">
                <i class="fas fa-microchip"></i>
              </div>
              <div class="device-info">
                <div class="device-name">{{ device.device_name || device.name || '未命名设备' }}</div>
                <div class="device-meta">
                  <span class="device-code">{{ device.device_code || device.code || '无编号' }}</span>
                  <span class="device-status" :class="getDeviceStatusClass(device.status)">
                    <i :class="getDeviceStatusIcon(device.status)"></i>
                    {{ getDeviceStatusText(device.status) }}
                  </span>
                </div>
              </div>
              <div class="device-stats">
                <div class="camera-count">{{ device.cameras ? device.cameras.length : 0 }}</div>
                <div class="count-label">摄像头</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：摄像头详情展示 -->
      <div class="right-panel">
        <div v-if="!selectedDevice && displayCameras.length === 0" class="no-selection">
          <div class="no-selection-icon">
            <i class="fas fa-video"></i>
          </div>
          <h3>选择设备查看摄像头</h3>
          <p>请从左侧设备列表中选择一个设备，查看该设备的摄像头监控信息</p>
          <div class="selection-features">
            <div class="feature-item">
              <i class="fas fa-eye"></i>
              <span>实时视频监控</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-expand"></i>
              <span>全屏观看模式</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-info-circle"></i>
              <span>设备详细信息</span>
            </div>
          </div>
        </div>

        <div v-else-if="displayCameras.length === 0" class="no-cameras">
          <div class="no-cameras-icon">
            <i class="fas fa-video-slash"></i>
          </div>
          <h3>该设备暂无摄像头</h3>
          <p>{{ selectedDevice ? `设备 "${selectedDevice.device_name || selectedDevice.name}" 暂未配置摄像头设备` : '当前选择的设备没有摄像头配置' }}</p>
        </div>

        <div v-else class="camera-detail-panel">
          <!-- 摄像头信息头部 -->
          <div class="camera-panel-header">
            <div class="panel-title">
              <h3>
                <i class="fas fa-video"></i>
                {{ selectedDevice ? `${selectedDevice.device_name || selectedDevice.name} - 摄像头监控` : '全部摄像头监控' }}
              </h3>
              <div class="camera-summary">
                <span class="total-count">{{ displayCameras.length }} 个摄像头</span>
                <span class="online-count">{{ onlineCameraCount }} 在线</span>
                <span class="offline-count" v-if="offlineCameraCount > 0">{{ offlineCameraCount }} 离线</span>
              </div>
            </div>
            <div class="panel-actions">
              <button class="action-btn" @click="refreshCameraData" :disabled="loading">
                <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
                刷新
              </button>
              <button class="action-btn" @click="toggleViewMode">
                <i class="fas fa-th" v-if="viewMode === 'list'"></i>
                <i class="fas fa-list" v-else></i>
                {{ viewMode === 'list' ? '网格' : '列表' }}
              </button>
            </div>
          </div>

          <!-- 摄像头监控区域 -->
          <div class="camera-monitor-section">
            <div v-if="loading" class="loading-container">
              <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
              </div>
              <span>加载摄像头数据中...</span>
            </div>

            <!-- 网格视图 -->
            <div v-else-if="viewMode === 'grid'" class="camera-grid-view">
              <div
                v-for="camera in displayCameras"
                :key="camera.id"
                class="camera-grid-item"
                :class="{ 'online': camera.status === 1, 'offline': camera.status !== 1 }"
                @click="selectCamera(camera)"
              >
                <div class="camera-preview">
                  <div v-if="camera.ezviz_url && camera.status === 1" class="video-preview">
                    <div
                      :ref="`videoContainer_${camera.id}`"
                      class="video-container"
                      @click.stop="playVideo(camera)"
                    >
                      <!-- Video.js 播放器将在这里动态创建 -->
                    </div>
                    <div class="video-overlay">
                      <button class="play-btn" @click.stop="playVideo(camera)" title="播放/暂停">
                        <i class="fas fa-play"></i>
                      </button>
                      <button class="fullscreen-btn" @click.stop="enterFullscreen(camera)" title="全屏观看">
                        <i class="fas fa-expand"></i>
                      </button>
                    </div>
                  </div>
                  <div v-else class="video-offline">
                    <div class="offline-icon">
                      <i class="fas fa-video-slash"></i>
                    </div>
                    <div class="offline-text">
                      <p>{{ camera.status === 1 ? '暂无视频流' : '摄像头离线' }}</p>
                    </div>
                  </div>
                </div>
                <div class="camera-info">
                  <div class="camera-name">
                    <i class="fas fa-video"></i>
                    {{ camera.camera_name || camera.name || '未命名摄像头' }}
                  </div>
                  <div class="camera-meta">
                    <span class="camera-type">{{ getCameraTypeText(camera.camera_type) }}</span>
                    <span class="camera-status" :class="getCameraStatusClass(camera.status)">
                      <i :class="getCameraStatusIcon(camera.status)"></i>
                      {{ getCameraStatusText(camera.status) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 列表视图 -->
            <div v-else class="camera-list-view">
              <div
                v-for="camera in displayCameras"
                :key="camera.id"
                class="camera-list-item"
                :class="{ 'online': camera.status === 1, 'offline': camera.status !== 1, 'selected': selectedCamera && selectedCamera.id === camera.id }"
                @click="selectCamera(camera)"
              >
                <div class="camera-list-header">
                  <div class="camera-basic-info">
                    <div class="camera-icon">
                      <i class="fas fa-video"></i>
                    </div>
                    <div class="camera-details">
                      <div class="camera-name">{{ camera.camera_name || camera.name || '未命名摄像头' }}</div>
                      <div class="camera-device">{{ camera.deviceName || '未知设备' }}</div>
                    </div>
                  </div>
                  <div class="camera-status-info">
                    <div class="status-indicator" :class="getCameraStatusClass(camera.status)">
                      <div class="status-dot"></div>
                      <span>{{ getCameraStatusText(camera.status) }}</span>
                    </div>
                    <div class="camera-type-badge">{{ getCameraTypeText(camera.camera_type) }}</div>
                  </div>
                  <div class="camera-actions">
                    <button
                      v-if="camera.ezviz_url && camera.status === 1"
                      class="action-btn play"
                      @click.stop="playVideo(camera)"
                      title="播放视频"
                    >
                      <i class="fas fa-play"></i>
                    </button>
                    <button
                      class="action-btn detail"
                      @click.stop="showCameraDetail(camera)"
                      title="详细信息"
                    >
                      <i class="fas fa-info-circle"></i>
                    </button>
                    <button
                      v-if="camera.ezviz_url && camera.status === 1"
                      class="action-btn fullscreen"
                      @click.stop="enterFullscreen(camera)"
                      title="全屏观看"
                    >
                      <i class="fas fa-expand"></i>
                    </button>
                  </div>
                </div>
                <div class="camera-list-body" v-if="selectedCamera && selectedCamera.id === camera.id">
                  <div class="camera-video-section">
                    <div v-if="camera.ezviz_url && camera.status === 1" class="video-player">
                      <div
                        :ref="`videoContainer_detail_${camera.id}`"
                        class="video-container-detail"
                      >
                        <!-- Video.js 播放器将在这里动态创建 -->
                      </div>
                    </div>
                    <div v-else class="video-unavailable">
                      <i class="fas fa-video-slash"></i>
                      <p>{{ camera.status === 1 ? '暂无视频流' : '摄像头离线，无法播放视频' }}</p>
                      <small>最后更新: {{ formatTime(camera.updated_at) }}</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 摄像头详情弹窗 -->
    <CameraDetailModal
      :visible="showModal"
      :camera="modalCamera"
      @close="closeModal"
    />
  </div>
</template>

<script>
import { getDeviceList, getProjectDevices, getDeviceCameras } from '@/api/device'
import CameraDetailModal from './CameraDetailModal.vue'

export default {
  name: 'CameraManagement',
  components: {
    CameraDetailModal
  },
  props: {
    projectId: {
      type: [String, Number],
      required: true
    },
    selectedProject: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      deviceList: [],
      selectedDevice: null,
      selectedCamera: null,
      allCameras: [], // 所有摄像头数据
      viewMode: 'grid', // 'grid' 或 'list'
      cameraStats: {
        total: 0,
        online: 0,
        offline: 0,
        devices: 0
      },
      videoStatus: {}, // 视频状态管理
      videoPlayers: {}, // Video.js播放器实例管理
      // 弹窗相关
      showModal: false,
      modalCamera: null
    }
  },
  computed: {
    projectName() {
      return this.selectedProject?.name || '未知项目'
    },
    // 当前显示的摄像头列表
    displayCameras() {
      if (this.selectedDevice) {
        // 显示选中设备的摄像头
        return this.allCameras.filter(camera => camera.device_id === this.selectedDevice.id)
      } else {
        // 显示所有摄像头
        return this.allCameras
      }
    },
    // 在线摄像头数量
    onlineCameraCount() {
      return this.displayCameras.filter(camera => camera.status === 1).length
    },
    // 离线摄像头数量
    offlineCameraCount() {
      return this.displayCameras.filter(camera => camera.status !== 1).length
    },
    // 在线百分比
    onlinePercentage() {
      if (this.cameraStats.total === 0) return 0
      return Math.round((this.cameraStats.online / this.cameraStats.total) * 100)
    },
    // 活跃设备数量
    activeDevices() {
      return this.deviceList.filter(device =>
        device.cameras && device.cameras.length > 0 && device.cameras.some(camera => camera.status === 1)
      ).length
    }
  },
  mounted() {
    this.fetchDeviceData()
  },
  updated() {
    // 当数据更新后，自动初始化有URL的在线摄像头播放器
    this.$nextTick(() => {
      this.autoInitializeVideos()
    })
  },
  beforeDestroy() {
    // 清理所有Video.js播放器实例
    Object.keys(this.videoPlayers).forEach(cameraId => {
      const player = this.videoPlayers[cameraId]
      if (player && typeof player.dispose === 'function') {
        try {
          player.dispose()
        } catch (error) {
          console.warn(`销毁摄像头 ${cameraId} 播放器失败:`, error)
        }
      }
    })
    this.videoPlayers = {}
  },
  methods: {
    // 获取设备数据
    async fetchDeviceData() {
      this.loading = true
      try {
        console.log('获取项目设备数据，项目ID:', this.projectId)

        // 优先使用项目专用API获取设备列表
        let response
        try {
          response = await getProjectDevices(this.projectId)
        } catch (error) {
          console.warn('项目专用设备API失败，使用通用API:', error)
          response = await getDeviceList({ project_id: this.projectId })
        }

        if (response && response.code === 0 && response.data) {
          this.deviceList = response.data.list || response.data || []
          this.allCameras = [] // 重置所有摄像头数据

          // 为每个设备获取摄像头信息
          for (let device of this.deviceList) {
            try {
              const cameraResponse = await getDeviceCameras(device.id)
              if (cameraResponse && cameraResponse.code === 0) {
                device.cameras = cameraResponse.data || []

                // 将摄像头添加到全局列表，并添加设备信息
                device.cameras.forEach(camera => {
                  camera.deviceName = device.device_name || device.name || '未命名设备'
                  camera.deviceId = device.id
                  this.allCameras.push(camera)
                })
              } else {
                device.cameras = []
              }
            } catch (error) {
              console.error(`获取设备 ${device.id} 的摄像头信息失败:`, error)
              device.cameras = []
            }
          }

          this.calculateCameraStats()

          // 数据加载完成后，自动初始化视频播放器
          this.$nextTick(() => {
            this.autoInitializeVideos()
          })
        } else {
          console.warn('获取设备列表失败:', response)
          this.deviceList = []
          this.allCameras = []
        }
      } catch (error) {
        console.error('获取设备数据失败:', error)
        this.deviceList = []
        this.allCameras = []
      } finally {
        this.loading = false
      }
    },

    // 计算摄像头统计信息
    calculateCameraStats() {
      const totalCameras = this.allCameras.length
      const onlineCameras = this.allCameras.filter(camera => camera.status === 1).length
      const offlineCameras = totalCameras - onlineCameras
      const devicesWithCameras = this.deviceList.filter(device =>
        device.cameras && device.cameras.length > 0
      ).length

      this.cameraStats = {
        total: totalCameras,
        online: onlineCameras,
        offline: offlineCameras,
        devices: devicesWithCameras
      }
    },

    // 选择设备
    selectDevice(device) {
      this.selectedDevice = device
      this.selectedCamera = null // 重置选中的摄像头
    },

    // 选择所有设备（显示所有摄像头）
    selectAllDevices() {
      this.selectedDevice = null
      this.selectedCamera = null // 重置选中的摄像头
    },

    // 选择摄像头
    selectCamera(camera) {
      this.selectedCamera = this.selectedCamera && this.selectedCamera.id === camera.id ? null : camera
      if (this.selectedCamera && this.viewMode === 'list') {
        // 在列表模式下选中摄像头时，初始化详细视频播放器
        this.$nextTick(() => {
          this.initDetailVideoPlayer(camera)
        })
      }
    },

    // 切换视图模式
    toggleViewMode() {
      this.viewMode = this.viewMode === 'grid' ? 'list' : 'grid'
      this.selectedCamera = null // 切换视图时重置选中的摄像头
    },

    // 刷新摄像头数据
    refreshCameraData() {
      this.fetchDeviceData()
    },

    // 获取设备状态类名
    getDeviceStatusClass(status) {
      return {
        'online': status === 1,
        'offline': status === 0 || status === null
      }
    },

    // 获取设备状态图标
    getDeviceStatusIcon(status) {
      return status === 1 ? 'fas fa-circle' : 'fas fa-circle'
    },

    // 获取设备状态文本
    getDeviceStatusText(status) {
      return status === 1 ? '在线' : '离线'
    },

    // 获取设备状态样式类
    getDeviceStatusClass(status) {
      return status === 1 ? 'online' : 'offline'
    },

    // 获取设备状态图标
    getDeviceStatusIcon(status) {
      return status === 1 ? 'fas fa-circle' : 'fas fa-times-circle'
    },

    // 获取摄像头状态类名
    getCameraStatusClass(status) {
      return {
        'online': status === 1,
        'offline': status === 0 || status === null
      }
    },

    // 获取摄像头状态图标
    getCameraStatusIcon(status) {
      return status === 1 ? 'fas fa-circle' : 'fas fa-circle'
    },

    // 获取摄像头状态文本
    getCameraStatusText(status) {
      return status === 1 ? '在线' : '离线'
    },

    // 获取摄像头类型文本
    getCameraTypeText(type) {
      const typeMap = {
        1: '球机',
        2: '枪机',
        3: '半球',
        4: '其他'
      }
      return typeMap[type] || '未知'
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '-'
      try {
        const date = new Date(timeStr)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return timeStr
      }
    },

    // 获取视频封面
    getVideoPoster() {
      // 可以返回一个默认的视频封面图片
      return ''
    },

    // 获取视频状态
    getVideoStatus(cameraId) {
      return this.videoStatus[cameraId] || ''
    },

    // 获取视频类型
    getVideoType(url) {
      if (!url) return 'video/mp4'

      const urlLower = url.toLowerCase()
      if (urlLower.includes('.m3u8') || urlLower.includes('hls')) {
        return 'application/x-mpegURL'
      } else if (urlLower.includes('.mp4')) {
        return 'video/mp4'
      } else if (urlLower.includes('.webm')) {
        return 'video/webm'
      } else if (urlLower.includes('.ogg')) {
        return 'video/ogg'
      } else if (urlLower.includes('rtmp')) {
        return 'rtmp/mp4'
      } else {
        // 默认尝试HLS格式
        return 'application/x-mpegURL'
      }
    },

    // 初始化Video.js播放器
    initVideoPlayer(camera) {
      // 检查是否已经初始化过
      if (this.videoPlayers[camera.id]) {
        // 如果已经初始化，尝试播放/暂停
        const player = this.videoPlayers[camera.id]
        if (player.paused()) {
          player.play().catch(error => {
            console.error('视频播放失败:', error)
            this.$set(this.videoStatus, camera.id, '播放失败')
          })
        } else {
          player.pause()
        }
        return
      }

      // 确保Video.js已加载
      if (typeof window.videojs === 'undefined') {
        console.error('Video.js not loaded!')
        this.$set(this.videoStatus, camera.id, '播放器未加载')
        return
      }

      const videoContainer = this.$refs[`videoContainer_${camera.id}`]
      if (!videoContainer) {
        console.error('找不到视频容器')
        return
      }

      // 确保videoContainer是DOM元素
      const containerElement = Array.isArray(videoContainer) ? videoContainer[0] : videoContainer
      if (!containerElement || typeof containerElement.appendChild !== 'function') {
        console.error('视频容器不是有效的DOM元素:', containerElement)
        return
      }

      // 清空容器
      containerElement.innerHTML = ''

      // 创建视频元素
      const videoElement = document.createElement('video')
      videoElement.className = 'video-js vjs-default-skin'
      videoElement.id = `camera-video-player-${camera.id}`
      videoElement.controls = true
      videoElement.preload = 'auto'
      videoElement.width = containerElement.clientWidth || 400
      videoElement.height = containerElement.clientHeight || 200 // 使用容器高度
      videoElement.poster = this.getVideoPoster()

      // 添加到容器
      containerElement.appendChild(videoElement)

      // 确定视频类型
      const videoType = this.getVideoType(camera.ezviz_url)
      console.log('初始化摄像头播放器，URL:', camera.ezviz_url, '类型:', videoType)

      // 创建播放器实例
      const videojs = window.videojs
      const player = videojs(`camera-video-player-${camera.id}`, {
        sources: [{
          src: camera.ezviz_url,
          type: videoType
        }],
        autoplay: true,
        muted: true, // 默认静音，避免自动播放策略限制
        controls: true,
        fluid: false, // 不使用流式布局，使用固定高度
        preload: 'auto',
        responsive: false, // 不使用响应式，使用固定尺寸
        width: containerElement.clientWidth || 400,
        height: containerElement.clientHeight || 200,
        html5: {
          vhs: {
            overrideNative: true
          },
          nativeVideoTracks: false,
          nativeAudioTracks: false,
          nativeTextTracks: false
        },
        controlBar: {
          children: [
            'playToggle',
            'volumePanel',
            'currentTimeDisplay',
            'timeDivider',
            'durationDisplay',
            'progressControl',
            'fullscreenToggle'
          ]
        }
      })

      // 存储播放器实例
      this.$set(this.videoPlayers, camera.id, player)

      // 设置加载状态
      this.$set(this.videoStatus, camera.id, '正在加载...')

      // 监听播放器事件
      player.ready(() => {
        console.log(`摄像头 ${camera.camera_name} 播放器准备就绪`)
        this.$set(this.videoStatus, camera.id, '')

        // 播放器准备就绪后尝试播放
        setTimeout(() => {
          player.play().catch(error => {
            console.warn(`摄像头 ${camera.camera_name} 自动播放失败:`, error)
            this.$set(this.videoStatus, camera.id, '点击播放')
          })
        }, 100)
      })

      player.on('loadstart', () => {
        this.$set(this.videoStatus, camera.id, '正在加载...')
      })

      player.on('canplay', () => {
        this.$set(this.videoStatus, camera.id, '')
        // 当视频可以播放时，尝试播放
        player.play().catch(error => {
          console.warn(`摄像头 ${camera.camera_name} 播放失败:`, error)
          this.$set(this.videoStatus, camera.id, '点击播放')
        })
      })

      player.on('play', () => {
        console.log(`摄像头 ${camera.camera_name} 开始播放`)
        this.$set(this.videoStatus, camera.id, '正在播放')
      })

      player.on('pause', () => {
        console.log(`摄像头 ${camera.camera_name} 暂停播放`)
        this.$set(this.videoStatus, camera.id, '已暂停')
      })

      player.on('error', (error) => {
        console.error(`摄像头 ${camera.camera_name} 播放错误:`, error)
        this.$set(this.videoStatus, camera.id, '播放失败')
      })

      player.on('ended', () => {
        console.log(`摄像头 ${camera.camera_name} 播放结束`)
        this.$set(this.videoStatus, camera.id, '播放结束')
      })
    },

    // 播放视频
    playVideo(camera) {
      const player = this.videoPlayers[camera.id]
      if (player) {
        if (player.paused()) {
          player.play().catch(error => {
            console.error('视频播放失败:', error)
            this.$set(this.videoStatus, camera.id, '播放失败')
            this.$message && this.$message.error('视频播放失败，请检查视频源')
          })
        } else {
          player.pause()
        }
      } else {
        // 如果播放器不存在，先初始化
        this.initVideoPlayer(camera)
      }
    },

    // 进入全屏观看
    enterFullscreen(camera) {
      const player = this.videoPlayers[camera.id]
      if (player) {
        // 使用Video.js的全屏功能
        if (player.requestFullscreen) {
          player.requestFullscreen()
        } else {
          // 如果Video.js全屏不可用，尝试原生全屏
          const videoElement = player.el().querySelector('video')
          if (videoElement) {
            if (videoElement.requestFullscreen) {
              videoElement.requestFullscreen()
            } else if (videoElement.webkitRequestFullscreen) {
              videoElement.webkitRequestFullscreen()
            } else if (videoElement.mozRequestFullScreen) {
              videoElement.mozRequestFullScreen()
            } else if (videoElement.msRequestFullscreen) {
              videoElement.msRequestFullscreen()
            }
          }
        }
      } else {
        // 如果播放器不存在，先初始化再全屏
        this.initVideoPlayer(camera)
        this.$nextTick(() => {
          this.enterFullscreen(camera)
        })
      }
    },

    // 初始化详细视频播放器（用于列表视图的展开详情）
    initDetailVideoPlayer(camera) {
      const detailPlayerId = `videoContainer_detail_${camera.id}`

      // 检查是否已经初始化过
      if (this.videoPlayers[detailPlayerId]) {
        return
      }

      // 确保Video.js已加载
      if (typeof window.videojs === 'undefined') {
        console.error('Video.js not loaded!')
        return
      }

      const videoContainer = this.$refs[detailPlayerId]
      if (!videoContainer) {
        console.error('找不到详细视频容器')
        return
      }

      // 确保videoContainer是DOM元素
      const containerElement = Array.isArray(videoContainer) ? videoContainer[0] : videoContainer
      if (!containerElement || typeof containerElement.appendChild !== 'function') {
        console.error('详细视频容器不是有效的DOM元素:', containerElement)
        return
      }

      // 清空容器
      containerElement.innerHTML = ''

      // 创建视频元素
      const videoElement = document.createElement('video')
      videoElement.className = 'video-js vjs-default-skin'
      videoElement.id = `camera-detail-video-player-${camera.id}`
      videoElement.controls = true
      videoElement.preload = 'auto'
      videoElement.width = containerElement.clientWidth || 600
      videoElement.height = 300
      videoElement.poster = this.getVideoPoster()

      // 添加到容器
      containerElement.appendChild(videoElement)

      // 确定视频类型
      const videoType = this.getVideoType(camera.ezviz_url)
      console.log('初始化详细摄像头播放器，URL:', camera.ezviz_url, '类型:', videoType)

      // 创建播放器实例
      const videojs = window.videojs
      const player = videojs(`camera-detail-video-player-${camera.id}`, {
        sources: [{
          src: camera.ezviz_url,
          type: videoType
        }],
        autoplay: true,
        muted: true,
        controls: true,
        fluid: false,
        preload: 'auto',
        responsive: false,
        width: containerElement.clientWidth || 600,
        height: 300,
        html5: {
          vhs: {
            overrideNative: true
          },
          nativeVideoTracks: false,
          nativeAudioTracks: false,
          nativeTextTracks: false
        }
      })

      // 存储播放器实例
      this.$set(this.videoPlayers, detailPlayerId, player)

      // 监听播放器事件
      player.ready(() => {
        console.log(`详细摄像头 ${camera.camera_name} 播放器准备就绪`)
        setTimeout(() => {
          player.play().catch(error => {
            console.warn(`详细摄像头 ${camera.camera_name} 自动播放失败:`, error)
          })
        }, 100)
      })

      player.on('error', (error) => {
        console.error(`详细摄像头 ${camera.camera_name} 播放错误:`, error)
      })
    },

    // 显示摄像头详情
    showCameraDetail(camera) {
      console.log('显示摄像头详情:', camera)

      // 转换摄像头数据格式以适配CameraDetailModal组件
      const modalCamera = {
        ...camera,
        id: camera.id,
        label: camera.camera_name || camera.name || '未命名摄像头', // CameraDetailModal期望的是label属性
        url: camera.ezviz_url || camera.url, // 使用ezviz_url作为主要URL
        ezviz_url: camera.ezviz_url, // 保留原始URL
        name: camera.camera_name || camera.name || '未命名摄像头',
        device: camera.deviceName || '未知设备', // CameraDetailModal期望的是device属性
        deviceName: camera.deviceName || '未知设备',
        status: camera.status,
        location: camera.location || '未设置',
        type: camera.camera_type, // 保留原始类型数值
        camera_type: camera.camera_type // 保留原始字段
      }

      this.modalCamera = modalCamera
      this.showModal = true
    },

    // 关闭摄像头详情弹窗
    closeModal() {
      this.showModal = false
      this.modalCamera = null
    },

    // 自动初始化有URL的在线摄像头视频播放器
    autoInitializeVideos() {
      // 延迟一点时间确保DOM已经渲染完成
      setTimeout(() => {
        console.log('开始自动初始化视频播放器，摄像头数量:', this.displayCameras.length)
        this.displayCameras.forEach(camera => {
          // 只为有URL且在线的摄像头自动初始化播放器
          if (camera.ezviz_url && camera.status === 1 && !this.videoPlayers[camera.id]) {
            console.log(`自动初始化摄像头 ${camera.camera_name} 的播放器，URL:`, camera.ezviz_url)

            // 检查DOM容器是否存在
            const containerRef = this.$refs[`videoContainer_${camera.id}`]
            if (containerRef) {
              console.log(`找到摄像头 ${camera.id} 的容器，开始初始化`)
              this.initVideoPlayer(camera)
            } else {
              console.warn(`未找到摄像头 ${camera.id} 的容器，跳过初始化`)
            }
          } else {
            if (!camera.ezviz_url) {
              console.log(`摄像头 ${camera.camera_name} 没有URL，跳过`)
            } else if (camera.status !== 1) {
              console.log(`摄像头 ${camera.camera_name} 离线，跳过`)
            } else if (this.videoPlayers[camera.id]) {
              console.log(`摄像头 ${camera.camera_name} 已初始化，跳过`)
            }
          }
        })
      }, 1000) // 增加延迟时间确保DOM渲染完成
    }
  }
}
</script>

<style scoped>
/* 摄像头监控中心样式 */
.camera-management-page {
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 15px;
  box-sizing: border-box;
  min-height: 0;
}

/* 页面主体内容 */
.page-content {
  display: flex;
  gap: 20px;
  height: 100%;
  overflow: hidden;
}

/* 左侧面板 */
.left-panel {
  width: 350px;
  min-width: 350px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: hidden;
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(0, 30, 60, 0.9), rgba(0, 50, 100, 0.7));
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

/* 摄像头统计概览 */
.camera-stats-dashboard {
  background: linear-gradient(135deg, rgba(0, 30, 60, 0.9), rgba(0, 50, 100, 0.7));
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 12px;
  padding: 15px;
  backdrop-filter: blur(10px);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.stats-header h3 {
  margin: 0;
  color: #7fdbff;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(127, 219, 255, 0.1);
  border: 1px solid rgba(127, 219, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #7fdbff;
}

.refresh-btn:hover {
  background: rgba(127, 219, 255, 0.2);
  border-color: #7fdbff;
  transform: scale(1.05);
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(0, 30, 60, 0.8), rgba(0, 50, 100, 0.6));
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 10px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #7fdbff, transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(127, 219, 255, 0.15);
  border-color: rgba(127, 219, 255, 0.6);
}

.stat-card:hover::before {
  opacity: 1;
}

/* 统计卡片图标样式 */
.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  color: #ffffff;
  background: linear-gradient(135deg, #7fdbff, #0074d9);
  box-shadow: 0 4px 15px rgba(127, 219, 255, 0.3);
}

.stat-card.online .stat-icon {
  background: linear-gradient(135deg, #2ecc40, #27ae60);
}

.stat-card.offline .stat-icon {
  background: linear-gradient(135deg, #ff4136, #e74c3c);
}

.stat-card.devices .stat-icon {
  background: linear-gradient(135deg, #ff851b, #f39c12);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #7fdbff;
  text-shadow: 0 0 10px rgba(127, 219, 255, 0.5);
  margin-bottom: 4px;
  font-family: 'Orbitron', monospace;
}

.stat-card.online .stat-number {
  color: #2ecc40;
  text-shadow: 0 0 10px rgba(46, 204, 64, 0.5);
}

.stat-card.offline .stat-number {
  color: #ff4136;
  text-shadow: 0 0 10px rgba(255, 65, 54, 0.5);
}

.stat-card.devices .stat-number {
  color: #ff851b;
  text-shadow: 0 0 10px rgba(255, 133, 27, 0.5);
}

.stat-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 0.7rem;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #2ecc40;
}

.stat-progress {
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #2ecc40, #27ae60);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.stat-alert {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.7rem;
  color: #ff4136;
}

.stat-detail {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
}

/* 设备选择部分 */
.device-selection-section {
  background: linear-gradient(135deg, rgba(0, 30, 60, 0.9), rgba(0, 50, 100, 0.7));
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 12px;
  padding: 15px;
  backdrop-filter: blur(10px);
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  margin: 0;
  color: #7fdbff;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-info {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}

.device-count {
  color: #7fdbff;
  font-weight: 500;
}

.device-selection-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-selection-item {
  background: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 10px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
}

.device-selection-item:hover {
  background: rgba(0, 40, 80, 0.8);
  border-color: rgba(127, 219, 255, 0.4);
  transform: translateX(3px);
}

.device-selection-item.selected {
  background: linear-gradient(135deg, rgba(127, 219, 255, 0.15), rgba(0, 74, 217, 0.2));
  border-color: #7fdbff;
  box-shadow: 0 0 15px rgba(127, 219, 255, 0.2);
}

.device-selection-item.all-devices {
  border-color: rgba(255, 193, 7, 0.3);
}

.device-selection-item.all-devices.selected {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 133, 27, 0.2));
  border-color: #ffc107;
  box-shadow: 0 0 15px rgba(255, 193, 7, 0.2);
}

.device-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(127, 219, 255, 0.2), rgba(0, 74, 217, 0.3));
  border: 1px solid rgba(127, 219, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7fdbff;
  font-size: 1.1rem;
}

.device-selection-item.all-devices .device-icon {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 133, 27, 0.3));
  border-color: rgba(255, 193, 7, 0.3);
  color: #ffc107;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
}

.device-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.8rem;
}

.device-code {
  color: rgba(255, 255, 255, 0.6);
}

.device-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.75rem;
}

.device-status.online {
  color: #2ecc40;
  background: rgba(46, 204, 64, 0.1);
}

.device-status.offline {
  color: #ff4136;
  background: rgba(255, 65, 54, 0.1);
}

.device-stats {
  text-align: center;
}

.camera-count {
  font-size: 1.2rem;
  font-weight: 700;
  color: #7fdbff;
  text-shadow: 0 0 8px rgba(127, 219, 255, 0.5);
}

.count-label {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 2px;
}

/* 右侧面板样式 */
.no-selection, .no-cameras {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  flex: 1;
}

.no-selection-icon, .no-cameras-icon {
  font-size: 4rem;
  color: #7fdbff;
  margin-bottom: 20px;
  opacity: 0.7;
}

.no-selection h3, .no-cameras h3 {
  margin: 0 0 15px 0;
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
}

.no-selection p, .no-cameras p {
  margin: 0 0 25px 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  line-height: 1.5;
  max-width: 400px;
}

.selection-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.feature-item i {
  color: #7fdbff;
  width: 20px;
}

/* 摄像头详情面板 */
.camera-detail-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.camera-panel-header {
  padding: 20px;
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
  background: rgba(0, 20, 40, 0.6);
}

.panel-title {
  margin-bottom: 12px;
}

.panel-title h3 {
  margin: 0 0 8px 0;
  color: #7fdbff;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.camera-summary {
  display: flex;
  gap: 15px;
  font-size: 0.85rem;
}

.total-count {
  color: #7fdbff;
  font-weight: 600;
}

.online-count {
  color: #2ecc40;
  font-weight: 500;
}

.offline-count {
  color: #ff4136;
  font-weight: 500;
}

.panel-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 8px 12px;
  background: rgba(127, 219, 255, 0.1);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 6px;
  color: #7fdbff;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  background: rgba(127, 219, 255, 0.2);
  border-color: #7fdbff;
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 摄像头监控区域 */
.camera-monitor-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 网格视图 */
.camera-grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.camera-grid-item {
  background: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.camera-grid-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(127, 219, 255, 0.15);
  border-color: rgba(127, 219, 255, 0.4);
}

.camera-grid-item.online {
  border-left: 3px solid #2ecc40;
}

.camera-grid-item.offline {
  border-left: 3px solid #ff4136;
}

.camera-preview {
  position: relative;
  height: 160px;
  background: rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-container {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.camera-preview:hover .video-overlay {
  opacity: 1;
}

.play-btn, .fullscreen-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(127, 219, 255, 0.5);
  color: #7fdbff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-btn:hover, .fullscreen-btn:hover {
  background: rgba(127, 219, 255, 0.2);
  border-color: #7fdbff;
}

.video-offline {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.offline-icon {
  font-size: 2.5rem;
  margin-bottom: 10px;
  color: #ff4136;
}

.offline-text p {
  margin: 0;
  font-size: 0.9rem;
}

.camera-info {
  padding: 12px;
}

.camera-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.camera-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
}

.camera-type {
  color: rgba(255, 255, 255, 0.7);
}

.camera-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.75rem;
}

.camera-status.online {
  color: #2ecc40;
  background: rgba(46, 204, 64, 0.1);
}

.camera-status.offline {
  color: #ff4136;
  background: rgba(255, 65, 54, 0.1);
}

/* 列表视图 */
.camera-list-view {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.camera-list-item {
  background: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(127, 219, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.camera-list-item:hover {
  border-color: rgba(127, 219, 255, 0.4);
  box-shadow: 0 4px 15px rgba(127, 219, 255, 0.1);
}

.camera-list-item.selected {
  border-color: #7fdbff;
  box-shadow: 0 0 20px rgba(127, 219, 255, 0.2);
}

.camera-list-item.online {
  border-left: 3px solid #2ecc40;
}

.camera-list-item.offline {
  border-left: 3px solid #ff4136;
}

.camera-list-header {
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.camera-basic-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.camera-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(127, 219, 255, 0.2), rgba(0, 74, 217, 0.3));
  border: 1px solid rgba(127, 219, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7fdbff;
  font-size: 1.1rem;
}

.camera-details {
  flex: 1;
}

.camera-details .camera-name {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
}

.camera-device {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
}

.camera-status-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.status-indicator.online {
  color: #2ecc40;
  background: rgba(46, 204, 64, 0.1);
  border: 1px solid rgba(46, 204, 64, 0.3);
}

.status-indicator.offline {
  color: #ff4136;
  background: rgba(255, 65, 54, 0.1);
  border: 1px solid rgba(255, 65, 54, 0.3);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

.camera-type-badge {
  padding: 4px 8px;
  background: rgba(127, 219, 255, 0.1);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 10px;
  font-size: 0.75rem;
  color: #7fdbff;
}

.camera-actions {
  display: flex;
  gap: 8px;
}

.action-btn.play {
  color: #2ecc40;
  border-color: rgba(46, 204, 64, 0.3);
}

.action-btn.play:hover {
  background: rgba(46, 204, 64, 0.1);
  border-color: #2ecc40;
}

.action-btn.detail {
  color: #7fdbff;
  border-color: rgba(127, 219, 255, 0.3);
}

.action-btn.detail:hover {
  background: rgba(127, 219, 255, 0.1);
  border-color: #7fdbff;
}

.action-btn.fullscreen {
  color: #ff851b;
  border-color: rgba(255, 133, 27, 0.3);
}

.action-btn.fullscreen:hover {
  background: rgba(255, 133, 27, 0.1);
  border-color: #ff851b;
}

.camera-list-body {
  border-top: 1px solid rgba(127, 219, 255, 0.2);
  padding: 15px;
  background: rgba(0, 10, 20, 0.5);
}

.camera-video-section {
  border-radius: 8px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.5);
}

.video-container-detail {
  width: 100%;
  height: 300px;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-unavailable {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.video-unavailable i {
  font-size: 3rem;
  margin-bottom: 15px;
  color: #ff4136;
}

.video-unavailable p {
  margin: 0 0 8px 0;
  font-size: 1rem;
}

.video-unavailable small {
  color: rgba(255, 255, 255, 0.4);
  font-size: 0.85rem;
}

/* 加载和无数据状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  flex: 1;
}

.loading-spinner {
  font-size: 2rem;
  color: #7fdbff;
  margin-bottom: 15px;
}

.loading-spinner i {
  animation: spin 1s linear infinite;
}

.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  flex: 1;
}

.no-data-container i {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #7fdbff;
  opacity: 0.7;
}

.no-data-container h3 {
  margin: 0 0 10px 0;
  color: #ffffff;
  font-size: 1.3rem;
  font-weight: 600;
}

.no-data-container p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
  max-width: 300px;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 滚动条样式 */
.device-selection-list::-webkit-scrollbar,
.camera-grid-view::-webkit-scrollbar,
.camera-list-view::-webkit-scrollbar {
  width: 8px;
}

.device-selection-list::-webkit-scrollbar-track,
.camera-grid-view::-webkit-scrollbar-track,
.camera-list-view::-webkit-scrollbar-track {
  background: rgba(0, 20, 40, 0.3);
  border-radius: 4px;
}

.device-selection-list::-webkit-scrollbar-thumb,
.camera-grid-view::-webkit-scrollbar-thumb,
.camera-list-view::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(127, 219, 255, 0.4), rgba(127, 219, 255, 0.6));
  border-radius: 4px;
  transition: all 0.3s ease;
}

.device-selection-list::-webkit-scrollbar-thumb:hover,
.camera-grid-view::-webkit-scrollbar-thumb:hover,
.camera-list-view::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(127, 219, 255, 0.6), rgba(127, 219, 255, 0.8));
}

/* Video.js 自定义样式 */
::v-deep .video-js {
  width: 100% !important;
  height: 100% !important;
  background-color: transparent !important;
  font-family: inherit !important;
}

::v-deep .vjs-default-skin {
  color: #00a8ff;
}

::v-deep .vjs-default-skin .vjs-big-play-button {
  background-color: rgba(0, 40, 80, 0.7);
  border-color: #00a8ff;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  line-height: 60px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s;
}

::v-deep .vjs-default-skin .vjs-big-play-button:hover {
  background-color: rgba(0, 168, 255, 0.2);
  border-color: #7fdbff;
}

::v-deep .vjs-default-skin .vjs-control-bar {
  background-color: rgba(0, 40, 80, 0.8);
  backdrop-filter: blur(5px);
}

::v-deep .vjs-default-skin .vjs-slider {
  background-color: rgba(0, 168, 255, 0.3);
}

::v-deep .vjs-default-skin .vjs-play-progress,
::v-deep .vjs-default-skin .vjs-volume-level {
  background-color: #00a8ff;
}

::v-deep .vjs-default-skin .vjs-control:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 168, 255, 0.5);
}

::v-deep .vjs-default-skin .vjs-button > .vjs-icon-placeholder:before {
  color: #ffffff;
}

::v-deep .vjs-default-skin .vjs-button:hover > .vjs-icon-placeholder:before {
  color: #7fdbff;
}

::v-deep .vjs-loading-spinner {
  border-color: #7fdbff transparent transparent transparent;
}

::v-deep .vjs-poster {
  background-size: cover;
  background-position: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel {
    width: 320px;
    min-width: 320px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .camera-grid-view {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
    padding: 15px;
  }
}

@media (max-width: 900px) {
  .page-content {
    flex-direction: column;
    gap: 15px;
  }

  .left-panel {
    width: 100%;
    min-width: auto;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .camera-grid-view {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    padding: 12px;
  }

  .camera-list-view {
    padding: 12px;
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .camera-management-page {
    padding: 10px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .stat-card {
    padding: 10px;
  }

  .stat-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .stat-number {
    font-size: 1.3rem;
  }

  .camera-grid-view {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 10px;
  }

  .camera-list-view {
    padding: 10px;
    gap: 8px;
  }

  .camera-panel-header {
    padding: 15px;
  }

  .panel-title h3 {
    font-size: 1.1rem;
  }

  .camera-summary {
    flex-direction: column;
    gap: 8px;
    font-size: 0.8rem;
  }

  .action-btn {
    padding: 6px 10px;
    font-size: 0.8rem;
  }
}


</style>
